<template>
  <template v-for="product of modelValue.buyerFromInsurer.raw.products">
    <template v-if="product.deliveryStatus">
      <p v-if="product.endDate && new Date(product.endDate) < new Date()">
        <q-icon class="text-red text-h4" name="o_warning" />
        {{ $t('afaktoApp.buyer.buyerFromInsurer.obsolete') }}
      </p>

      <q-table
        :columns="columns"
        :hide-pagination="true"
        :pagination="{ rowsPerPage: 0 }"
        :rows="rows(product)"
        bordered
        class="q-mx-auto"
        flat
        hide-header
        row-key="label"
        separator="cell"
        style="max-width: 50em"
      >
        <template v-slot:body-cell="props" style="width: 50%">
          <q-td :props="props" class="q-pa-none">
            <div v-if="props.col.name === 'label'" class="text-weight-bold" style="text-align: left">
               <span style="display: inline-flex; align-items: center; gap: 4px;">
                {{ props.row.label }}
                <q-icon
                  v-if="props.row.tooltip"
                  name="info"
                  size="16px"
                >
                  <q-tooltip>{{ props.row.tooltip }}</q-tooltip>
                </q-icon>
             </span>
            </div>

            <div v-else-if="props.col.name === 'value'" :style="{ textAlign: props.row.align }">

              <!-- Special case for Decision row -->
              <p>bite</p>
              <template v-if="props.row.label === 'Insurer decision'">
                <div v-if="getInsurerDecisionCode(product)">
                  {{ $t(`afaktoApp.creditLimit.insurer.coface.decision.${getInsurerDecisionCode(product)}`) }}
                </div>
                <q-btn
                  v-else-if="product.deliveryStatus !== 'DECIDED_FULL'"
                  :label="t('afaktoApp.creditLimitRequest.requestInsurerDecision')"
                  color="primary"
                  size="sm"
                  @click="requestDecisionReason(product.deliveryId)"
                />
              </template>

              <!-- Regular rendering for all other rows -->
              <template v-else>
                <div :style="{ textAlign: props.row.align }">
                  {{ props.row.value }}
                </div>
              </template>
            </div>
          </q-td>
        </template>

        <!--        <template #body-cell-insurerComment="props">-->
        <!--          <q-td :props="props">-->
        <!--            <div v-if="props.row.insurerComment">-->
        <!--              {{ props.row.insurerComment }}-->
        <!--            </div>-->
        <!--            <q-btn-->
        <!--              v-else-if="props.row.insurerDecision === 'GA01'"-->
        <!--              size="sm"-->
        <!--              color="primary"-->
        <!--              :label="t(`afaktoApp.creditLimitRequest.requestInsurerComment`)"-->
        <!--              @click="requestDecisionReason(props.row)"-->
        <!--            />-->
        <!--          </q-td>-->
        <!--        </template>-->
      </q-table>
    </template>
  </template>
</template>

<script setup>
import { format } from '/src/util/format';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import BuyerService from 'pages/entities/buyer/buyer.service.js';
import useNotifications from 'src/util/useNotifications.js';

const { n, t } = useI18n();

const { notifyError } = useNotifications();

const model = defineModel({ type: Object });

const rows = computed(() => product => [
  { label: 'Type', value: t(`afaktoApp.creditLimit.insurer.coface.productType.${product.productCode}`), align: 'left' },
  {
    label: 'Decision',
    value: t(`afaktoApp.creditLimit.insurer.coface.deliveryStatus.${product.deliveryStatus}`),
    align: 'left'
  },
  { label: 'Insurer decision', align: 'left' },
  {
    label: 'Approved Amount',
    value: n(product.position.amount.value, 'currencyCode', { currency: product.position.amount.currency }),
    align: 'right'
  },
  { label: 'Decision date', value: format(product.updateDate, 'yyyy-MM-dd'), align: 'right' },
  { label: 'Effect from date', value: format(product.effectDate, 'yyyy-MM-dd'), align: 'right' }
]);

const columns = [
  { name: 'label', required: true, label: 'Label', field: 'label' },
  { name: 'value', required: true, label: 'Value', field: 'value' }
];

const props = defineProps({
  contract: Object
});

const getInsurerDecisionCode = (product) => {
  const rawDecisionProduct = model.value?.buyerFromInsurer?.rawDecision?.product;

  if (rawDecisionProduct?.deliveryId !== product.deliveryId) return null;

  return rawDecisionProduct.creditLimitPeriods?.[0]?.creditPeriodCondition?.reasonCodes?.[0]?.code;
};

async function requestDecisionReason(deliveryId) {
  try {
    const buyerFromInsurer = await BuyerService.getInsurerDecision(model.value.id, deliveryId);
    model.value.buyerFromInsurer = buyerFromInsurer.data;
  } catch (err) {
    notifyError(err);
  }
}


</script>
