<template>
  <div class="q-pa-md">
    <credit-limit-requests-table :buyer-id="buyerId" :rows-per-page="20"></credit-limit-requests-table>
    lalala
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';

import CreditLimitRequestsTable from './CreditLimitRequestsTable.vue';

const route = useRoute();

// /buyers/:id/credit-limit-requests
const buyerId = ref(route.params.id);
</script>
